# Excel to Files Converter

A Python tool that converts Excel files containing chapter data into individual text files. Designed with Vietnamese text support and robust filename handling.

## Features

- ✅ **Vietnamese Text Support**: Full UTF-8 encoding support for Vietnamese characters
- ✅ **Filename Sanitization**: Automatically handles invalid filename characters
- ✅ **Duplicate Handling**: Manages duplicate chapter names by appending numbers
- ✅ **Multiple Output Formats**: Supports both `.txt` and `.md` (Markdown) formats
- ✅ **Error Handling**: Comprehensive error handling with detailed feedback
- ✅ **CLI Interface**: Easy-to-use command-line interface
- ✅ **Cross-Platform**: Works on Windows, macOS, and Linux

## Requirements

- Python 3.7+
- pandas
- openpyxl
- click

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Excel File Format

Your Excel file must contain at least these two columns:

| Column Name | Description |
|-------------|-------------|
| `chapter` | Chapter names/titles |
| `chapter-content` | Chapter content/text |

### Example Excel Structure:
```
| chapter                    | chapter-content                           |
|----------------------------|-------------------------------------------|
| Chương 1: Giới thiệu      | Nội dung chương 1 với tiếng Việt...      |
| Chương 2: Lịch sử         | Nội dung chương 2 với tiếng Việt...      |
```

## Usage

### Basic Usage
```bash
python excel_to_files.py your_excel_file.xlsx
```

### Advanced Usage
```bash
# Specify output directory
python excel_to_files.py your_excel_file.xlsx --output-dir my_chapters

# Use Markdown format
python excel_to_files.py your_excel_file.xlsx --format md

# Combine options
python excel_to_files.py your_excel_file.xlsx --output-dir chapters --format md
```

### Command Line Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--output-dir` | `-o` | Output directory for generated files | `output` |
| `--format` | `-f` | Output format: `txt` or `md` | `txt` |
| `--encoding` | | Text encoding for output files | `utf-8` |

## Output

### Text Format (`.txt`)
```
Chapter Title
=============

Chapter content goes here...
```

### Markdown Format (`.md`)
```markdown
# Chapter Title

Chapter content goes here...
```

## Filename Handling

### Sanitization
Invalid filename characters are automatically replaced:
- `< > : " | ? * \ /` → `_` (underscore)
- Leading/trailing spaces and dots are removed
- Long filenames are truncated to 200 characters
- Empty names become "untitled"

### Duplicate Handling
When duplicate chapter names are found:
- First occurrence: `chapter_name.txt`
- Second occurrence: `chapter_name_2.txt`
- Third occurrence: `chapter_name_3.txt`
- And so on...

## Example Workflow

1. **Create a sample Excel file** (optional):
   ```bash
   python create_sample_excel.py
   ```

2. **Convert the Excel file**:
   ```bash
   python excel_to_files.py sample_chapters.xlsx
   ```

3. **Check the output**:
   ```bash
   ls output/
   ```

## Error Handling

The converter handles various error scenarios:

- ❌ **Missing Excel file**: Clear error message with file path
- ❌ **Missing required columns**: Lists available columns
- ❌ **Empty rows**: Skips rows with missing chapter names or content
- ❌ **File write errors**: Reports specific files that failed to write
- ❌ **Invalid Excel format**: Provides detailed error information

## Vietnamese Text Support

This tool is specifically designed to handle Vietnamese text properly:

- Uses UTF-8 encoding for all file operations
- Preserves Vietnamese diacritics (á, à, ả, ã, ạ, etc.)
- Handles Vietnamese characters in both chapter names and content
- Maintains proper text formatting and line breaks

## Troubleshooting

### Common Issues

1. **"Missing required columns" error**:
   - Ensure your Excel file has columns named exactly `chapter` and `chapter-content`
   - Check for extra spaces in column names

2. **Vietnamese characters not displaying correctly**:
   - Ensure your text editor supports UTF-8 encoding
   - Try opening files with a different text editor

3. **Permission errors**:
   - Check that you have write permissions in the output directory
   - Try running with administrator/sudo privileges if needed

4. **Excel file not found**:
   - Verify the file path is correct
   - Use absolute paths if relative paths don't work

### Getting Help

Run the help command to see all available options:
```bash
python excel_to_files.py --help
```

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.
